/**
 * REAL-TIME AGENT UPDATES COMPONENT
 * 
 * React component that provides real-time updates on agent status, collaboration,
 * and communication using WebSocket connections to the backend agent system.
 */

import React, { useState, useEffect, useCallback } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '../ui/card';
import { Badge } from '../ui/badge';
import { Alert, AlertDescription } from '../ui/alert';
import { Button } from '../ui/button';
import { 
  Users, 
  MessageCircle, 
  Activity, 
  Wifi, 
  WifiOff,
  Clock,
  CheckCircle,
  AlertCircle,
  Zap
} from 'lucide-react';

const RealTimeAgentUpdates = ({ 
  sessionId, 
  onAgentStatusChange, 
  onCollaborationUpdate,
  isVisible = true 
}) => {
  const [connectionStatus, setConnectionStatus] = useState('disconnected');
  const [agentStatuses, setAgentStatuses] = useState([]);
  const [recentMessages, setRecentMessages] = useState([]);
  const [collaborationActive, setCollaborationActive] = useState(false);
  const [lastUpdate, setLastUpdate] = useState(null);

  // Simulate WebSocket connection (in production, this would be a real WebSocket)
  useEffect(() => {
    if (!sessionId) return;

    console.log('🔗 Establishing real-time connection for session:', sessionId);
    setConnectionStatus('connecting');

    // Simulate connection establishment
    const connectionTimer = setTimeout(() => {
      setConnectionStatus('connected');
      console.log('✅ Real-time connection established');
      
      // Initialize with current agent statuses
      initializeAgentStatuses();
      
      // Start periodic updates simulation
      startRealTimeUpdates();
    }, 1000);

    return () => {
      clearTimeout(connectionTimer);
      setConnectionStatus('disconnected');
      console.log('🔌 Real-time connection closed');
    };
  }, [sessionId]);

  const initializeAgentStatuses = () => {
    // Simulate getting current agent statuses
    const mockAgentStatuses = [
      {
        id: 'triage-001',
        name: 'Nurse Triage Specialist',
        role: 'triage',
        status: 'online',
        lastActivity: new Date().toISOString(),
        currentSessions: [sessionId],
        responseTime: 1200
      },
      {
        id: 'gp-001',
        name: 'Dr. Sarah Chen',
        role: 'general_practitioner',
        status: 'online',
        lastActivity: new Date().toISOString(),
        currentSessions: [],
        responseTime: 1800
      },
      {
        id: 'cardio-001',
        name: 'Dr. Michael Rodriguez',
        role: 'cardiologist',
        status: 'online',
        lastActivity: new Date().toISOString(),
        currentSessions: [],
        responseTime: 2100
      },
      {
        id: 'emergency-001',
        name: 'Dr. Emergency Response',
        role: 'emergency',
        status: 'online',
        lastActivity: new Date().toISOString(),
        currentSessions: [],
        responseTime: 800
      }
    ];

    setAgentStatuses(mockAgentStatuses);
  };

  const startRealTimeUpdates = () => {
    // Simulate real-time updates every 5 seconds
    const updateInterval = setInterval(() => {
      // Simulate random agent status updates
      if (Math.random() > 0.7) {
        simulateAgentStatusUpdate();
      }
      
      // Simulate collaboration messages
      if (Math.random() > 0.8) {
        simulateCollaborationMessage();
      }
      
      setLastUpdate(new Date().toISOString());
    }, 5000);

    return () => clearInterval(updateInterval);
  };

  const simulateAgentStatusUpdate = () => {
    setAgentStatuses(prev => {
      const updated = [...prev];
      const randomIndex = Math.floor(Math.random() * updated.length);
      const agent = updated[randomIndex];
      
      // Simulate status changes
      const possibleStatuses = ['online', 'busy', 'away'];
      const newStatus = possibleStatuses[Math.floor(Math.random() * possibleStatuses.length)];
      
      updated[randomIndex] = {
        ...agent,
        status: newStatus,
        lastActivity: new Date().toISOString(),
        responseTime: Math.floor(Math.random() * 2000) + 800
      };

      // Notify parent component
      if (onAgentStatusChange) {
        onAgentStatusChange(updated[randomIndex]);
      }

      return updated;
    });
  };

  const simulateCollaborationMessage = () => {
    const collaborationMessages = [
      'Agent handoff initiated to cardiology specialist',
      'Multi-agent consultation requested for complex case',
      'Emergency protocol activated - all agents notified',
      'Patient risk assessment updated by predictive analytics',
      'Collaborative diagnosis in progress',
      'Treatment recommendations synthesized from multiple agents'
    ];

    const newMessage = {
      id: Date.now(),
      type: 'collaboration',
      message: collaborationMessages[Math.floor(Math.random() * collaborationMessages.length)],
      timestamp: new Date().toISOString(),
      priority: Math.random() > 0.7 ? 'high' : 'normal'
    };

    setRecentMessages(prev => [newMessage, ...prev.slice(0, 4)]);
    setCollaborationActive(true);

    // Notify parent component
    if (onCollaborationUpdate) {
      onCollaborationUpdate(newMessage);
    }

    // Reset collaboration status after 10 seconds
    setTimeout(() => setCollaborationActive(false), 10000);
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'online': return 'bg-green-500';
      case 'busy': return 'bg-yellow-500';
      case 'away': return 'bg-orange-500';
      case 'offline': return 'bg-gray-500';
      default: return 'bg-gray-500';
    }
  };

  const getStatusIcon = (status) => {
    switch (status) {
      case 'online': return <CheckCircle className="h-4 w-4 text-green-600" />;
      case 'busy': return <Clock className="h-4 w-4 text-yellow-600" />;
      case 'away': return <AlertCircle className="h-4 w-4 text-orange-600" />;
      case 'offline': return <WifiOff className="h-4 w-4 text-gray-600" />;
      default: return <Activity className="h-4 w-4 text-gray-600" />;
    }
  };

  const getConnectionIcon = () => {
    switch (connectionStatus) {
      case 'connected': return <Wifi className="h-4 w-4 text-green-600" />;
      case 'connecting': return <Activity className="h-4 w-4 text-yellow-600 animate-pulse" />;
      case 'disconnected': return <WifiOff className="h-4 w-4 text-red-600" />;
      default: return <WifiOff className="h-4 w-4 text-gray-600" />;
    }
  };

  const formatTime = (timestamp) => {
    return new Date(timestamp).toLocaleTimeString();
  };

  const reconnect = () => {
    setConnectionStatus('connecting');
    setTimeout(() => {
      setConnectionStatus('connected');
      initializeAgentStatuses();
    }, 2000);
  };

  if (!isVisible) {
    return null;
  }

  return (
    <Card className="w-full max-w-md bg-gradient-to-br from-green-50 to-blue-50 border-green-200">
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-2 text-green-800">
            <Users className="h-5 w-5" />
            Real-Time Agent Status
          </CardTitle>
          <div className="flex items-center gap-2">
            {getConnectionIcon()}
            <Badge 
              variant={connectionStatus === 'connected' ? 'default' : 'secondary'}
              className="capitalize"
            >
              {connectionStatus}
            </Badge>
          </div>
        </div>
      </CardHeader>

      <CardContent className="space-y-4">
        {/* Connection Status */}
        {connectionStatus === 'disconnected' && (
          <Alert className="border-red-200 bg-red-50">
            <WifiOff className="h-4 w-4 text-red-600" />
            <AlertDescription className="text-red-800">
              Real-time connection lost. 
              <Button 
                variant="link" 
                className="p-0 h-auto text-red-800 underline ml-1"
                onClick={reconnect}
              >
                Reconnect
              </Button>
            </AlertDescription>
          </Alert>
        )}

        {/* Agent Status List */}
        <div className="space-y-2">
          <h3 className="text-sm font-semibold text-gray-700">Agent Status</h3>
          <div className="space-y-2">
            {agentStatuses.map((agent) => (
              <div 
                key={agent.id} 
                className="flex items-center justify-between p-2 bg-white rounded border border-gray-200 hover:border-blue-300 transition-colors"
              >
                <div className="flex items-center gap-2">
                  <div className={`w-2 h-2 rounded-full ${getStatusColor(agent.status)}`}></div>
                  <div>
                    <div className="text-sm font-medium text-gray-800">{agent.name}</div>
                    <div className="text-xs text-gray-500 capitalize">{agent.role.replace('_', ' ')}</div>
                  </div>
                </div>
                <div className="flex items-center gap-2">
                  {getStatusIcon(agent.status)}
                  <span className="text-xs text-gray-500">{agent.responseTime}ms</span>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Collaboration Status */}
        {collaborationActive && (
          <Alert className="border-blue-200 bg-blue-50">
            <Zap className="h-4 w-4 text-blue-600 animate-pulse" />
            <AlertDescription className="text-blue-800">
              <strong>Multi-Agent Collaboration Active</strong>
              <br />
              Agents are collaborating on your case in real-time.
            </AlertDescription>
          </Alert>
        )}

        {/* Recent Messages */}
        {recentMessages.length > 0 && (
          <div className="space-y-2">
            <h3 className="text-sm font-semibold text-gray-700">Recent Activity</h3>
            <div className="space-y-2 max-h-32 overflow-y-auto">
              {recentMessages.map((message) => (
                <div 
                  key={message.id} 
                  className={`p-2 rounded text-xs ${
                    message.priority === 'high' 
                      ? 'bg-orange-50 border border-orange-200 text-orange-800' 
                      : 'bg-gray-50 border border-gray-200 text-gray-700'
                  }`}
                >
                  <div className="flex items-center gap-1 mb-1">
                    <MessageCircle className="h-3 w-3" />
                    <span className="font-medium">{formatTime(message.timestamp)}</span>
                  </div>
                  <div>{message.message}</div>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Last Update */}
        {lastUpdate && (
          <div className="text-xs text-gray-500 text-center">
            Last updated: {formatTime(lastUpdate)}
          </div>
        )}

        {/* System Status */}
        <div className="grid grid-cols-2 gap-2 text-center">
          <div className="p-2 bg-green-50 rounded border border-green-200">
            <div className="text-lg font-bold text-green-800">
              {agentStatuses.filter(a => a.status === 'online').length}
            </div>
            <div className="text-xs text-green-600">Online</div>
          </div>
          <div className="p-2 bg-blue-50 rounded border border-blue-200">
            <div className="text-lg font-bold text-blue-800">
              {agentStatuses.filter(a => a.currentSessions.includes(sessionId)).length}
            </div>
            <div className="text-xs text-blue-600">Active</div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default RealTimeAgentUpdates;
