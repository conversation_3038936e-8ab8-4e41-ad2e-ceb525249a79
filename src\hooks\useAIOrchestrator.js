import { useState, useEffect, useCallback } from 'react';
import { aiOrchestrator } from '../services/aiOrchestrator';
import { agentOrchestrator } from '../services/AgentOrchestrator';
import { agentRegistry } from '../services/AgentRegistry';

/**
 * Hook for managing AI Orchestrator interactions
 */
export function useAIOrchestrator() {
  const [isInitialized, setIsInitialized] = useState(false);
  const [providers, setProviders] = useState([]);
  const [healthStatus, setHealthStatus] = useState({});
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState(null);

  // Initialize orchestrator on mount
  useEffect(() => {
    const initializeOrchestrator = async () => {
      try {
        setIsLoading(true);
        await aiOrchestrator.initialize();
        setProviders(aiOrchestrator.getAvailableProviders());
        setIsInitialized(true);
        console.log('✅ AI Orchestrator initialized successfully');
      } catch (err) {
        console.error('❌ Failed to initialize AI Orchestrator:', err);
        setError(err.message);
      } finally {
        setIsLoading(false);
      }
    };

    initializeOrchestrator();
  }, []);

  // Check provider health periodically
  useEffect(() => {
    if (!isInitialized) return;

    const checkHealth = async () => {
      try {
        const status = await aiOrchestrator.checkProvidersHealth();
        setHealthStatus(status);
      } catch (err) {
        console.warn('⚠️ Health check failed:', err);
      }
    };

    // Initial health check
    checkHealth();

    // Set up periodic health checks every 5 minutes
    const healthInterval = setInterval(checkHealth, 5 * 60 * 1000);

    return () => clearInterval(healthInterval);
  }, [isInitialized]);

  // Generate AI response
  const generateResponse = useCallback(async (request) => {
    if (!isInitialized) {
      throw new Error('AI Orchestrator not initialized');
    }

    setIsLoading(true);
    setError(null);

    try {
      console.log('🤖 Generating response with AI Orchestrator...', {
        agentType: request.agentType,
        complexity: request.complexity,
        messageCount: request.messages?.length
      });

      const response = await aiOrchestrator.generateResponse(request);
      
      console.log('✅ AI response generated successfully', {
        provider: response.provider,
        confidence: response.confidence,
        tokenUsage: response.usage
      });

      return response;
    } catch (err) {
      console.error('❌ AI response generation failed:', err);
      setError(err.message);
      throw err;
    } finally {
      setIsLoading(false);
    }
  }, [isInitialized]);

  // Select best provider for request
  const selectProvider = useCallback((complexity, options = {}) => {
    if (!isInitialized) {
      throw new Error('AI Orchestrator not initialized');
    }

    return aiOrchestrator.selectProvider(complexity, options);
  }, [isInitialized]);

  // Get provider statistics
  const getProviderStats = useCallback(() => {
    if (!isInitialized) return null;
    return aiOrchestrator.getProviderStats();
  }, [isInitialized]);

  // Reset error state
  const clearError = useCallback(() => {
    setError(null);
  }, []);

  // Send message through agent orchestrator (new multi-agent system)
  const sendMessageToAgent = useCallback(async (message, options = {}) => {
    if (!message?.trim()) {
      setError('Message cannot be empty');
      return null;
    }

    setIsLoading(true);
    setError(null);

    try {
      console.log('🎭 Sending message to Agent Orchestrator...', {
        sessionId: options.sessionId,
        urgencyLevel: options.urgencyLevel,
        capabilities: options.requestedCapabilities
      });

      // Initialize agent orchestrator if needed
      if (!agentOrchestrator.getOrchestratorStats().activeSessions) {
        await agentOrchestrator.initialize();
      }

      const orchestrationResponse = await agentOrchestrator.processRequest({
        sessionId: options.sessionId || 'default-session',
        userMessage: message,
        currentAgentId: options.currentAgentId,
        requestedCapabilities: options.requestedCapabilities,
        urgencyLevel: options.urgencyLevel || 'medium',
        patientContext: options.patientContext
      });

      if (orchestrationResponse) {
        const { agentResponse, handoffOccurred, previousAgent, nextRecommendedAgent } = orchestrationResponse;

        console.log('✅ Agent response generated successfully', {
          agent: agentResponse.agentName,
          confidence: agentResponse.confidence,
          handoffOccurred
        });

        return {
          success: true,
          data: {
            content: agentResponse.content,
            agentId: agentResponse.agentId,
            agentName: agentResponse.agentName,
            confidence: agentResponse.confidence,
            reasoning: agentResponse.reasoning,
            handoffOccurred,
            previousAgent,
            nextRecommendedAgent,
            suggestedHandoffs: agentResponse.suggestedHandoffs,
            emergencyFlags: agentResponse.emergencyFlags,
            followUpActions: agentResponse.followUpActions,
            metadata: agentResponse.metadata
          }
        };
      } else {
        throw new Error('No response from agent orchestrator');
      }
    } catch (err) {
      console.error('❌ Agent Orchestrator Error:', err);
      setError(err.message);

      // Return error response
      return {
        success: false,
        error: err.message || 'Agent orchestrator failed'
      };
    } finally {
      setIsLoading(false);
    }
  }, []);

  // Get available medical agents from the real agent registry
  const getAvailableAgents = useCallback(() => {
    try {
      const allAgents = agentRegistry.getAllAgents();
      return allAgents.map(agent => ({
        id: agent.id,
        name: agent.name,
        specialization: agent.role.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase()),
        description: `${agent.role.replace('_', ' ')} specialist with capabilities: ${agent.capabilities.join(', ')}`,
        role: agent.role,
        capabilities: agent.capabilities,
        isActive: agent.isActive,
        confidence: agent.getPerformanceMetrics().averageConfidence || 0.8
      }));
    } catch (error) {
      console.error('Error getting available agents:', error);
      // Fallback to basic agent list if registry fails
      return [
        {
          id: 'fallback-gp',
          name: 'General Practitioner',
          specialization: 'General Medicine',
          description: 'Primary care physician for general health consultations',
          role: 'general_practitioner',
          capabilities: ['primary_care'],
          isActive: true,
          confidence: 0.8
        }
      ];
    }
  }, []);

  return {
    // State
    isInitialized,
    isLoading,
    error,
    providers,
    healthStatus,

    // Actions
    generateResponse,
    sendMessageToAgent, // New multi-agent messaging
    selectProvider,
    getProviderStats,
    getAvailableAgents,
    clearError,

    // Agent System Access
    agentOrchestrator,
    agentRegistry,

    // Computed
    isHealthy: isInitialized && Object.values(healthStatus).some(status => status === 'healthy'),
    availableProviders: providers.filter(p => healthStatus[p] === 'healthy')
  };
}
