import { useState, useEffect, useCallback } from 'react';
import { aiOrchestrator } from '../services/aiOrchestrator';
import { agentOrchestrator } from '../services/AgentOrchestrator';
import { agentRegistry } from '../services/AgentRegistry';

/**
 * Hook for managing AI Orchestrator interactions
 */
export function useAIOrchestrator() {
  const [isInitialized, setIsInitialized] = useState(false);
  const [providers, setProviders] = useState([]);
  const [healthStatus, setHealthStatus] = useState({});
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState(null);

  // Initialize orchestrator on mount
  useEffect(() => {
    const initializeOrchestrator = async () => {
      try {
        setIsLoading(true);
        await aiOrchestrator.initialize();
        setProviders(aiOrchestrator.getAvailableProviders());
        setIsInitialized(true);
        console.log('✅ AI Orchestrator initialized successfully');
      } catch (err) {
        console.error('❌ Failed to initialize AI Orchestrator:', err);
        setError(err.message);
      } finally {
        setIsLoading(false);
      }
    };

    initializeOrchestrator();
  }, []);

  // Check provider health periodically
  useEffect(() => {
    if (!isInitialized) return;

    const checkHealth = async () => {
      try {
        const status = await aiOrchestrator.checkProvidersHealth();
        setHealthStatus(status);
      } catch (err) {
        console.warn('⚠️ Health check failed:', err);
      }
    };

    // Initial health check
    checkHealth();

    // Set up periodic health checks every 5 minutes
    const healthInterval = setInterval(checkHealth, 5 * 60 * 1000);

    return () => clearInterval(healthInterval);
  }, [isInitialized]);

  // Generate AI response
  const generateResponse = useCallback(async (request) => {
    if (!isInitialized) {
      throw new Error('AI Orchestrator not initialized');
    }

    setIsLoading(true);
    setError(null);

    try {
      console.log('🤖 Generating response with AI Orchestrator...', {
        agentType: request.agentType,
        complexity: request.complexity,
        messageCount: request.messages?.length
      });

      const response = await aiOrchestrator.generateResponse(request);
      
      console.log('✅ AI response generated successfully', {
        provider: response.provider,
        confidence: response.confidence,
        tokenUsage: response.usage
      });

      return response;
    } catch (err) {
      console.error('❌ AI response generation failed:', err);
      setError(err.message);
      throw err;
    } finally {
      setIsLoading(false);
    }
  }, [isInitialized]);

  // Select best provider for request
  const selectProvider = useCallback((complexity, options = {}) => {
    if (!isInitialized) {
      throw new Error('AI Orchestrator not initialized');
    }

    return aiOrchestrator.selectProvider(complexity, options);
  }, [isInitialized]);

  // Get provider statistics
  const getProviderStats = useCallback(() => {
    if (!isInitialized) return null;
    return aiOrchestrator.getProviderStats();
  }, [isInitialized]);

  // Reset error state
  const clearError = useCallback(() => {
    setError(null);
  }, []);

  // Send message through agent orchestrator with advanced context (enhanced multi-agent system)
  const sendMessageToAgent = useCallback(async (message, options = {}) => {
    if (!message?.trim()) {
      setError('Message cannot be empty');
      return null;
    }

    setIsLoading(true);
    setError(null);

    try {
      console.log('🧠 Sending message to Agent Orchestrator with Advanced Context...', {
        sessionId: options.sessionId,
        urgencyLevel: options.urgencyLevel,
        capabilities: options.requestedCapabilities,
        contextEnhanced: true
      });

      // Initialize agent orchestrator if needed
      if (!agentOrchestrator.getOrchestratorStats().activeSessions) {
        await agentOrchestrator.initialize();
      }

      const orchestrationResponse = await agentOrchestrator.processRequest({
        sessionId: options.sessionId || 'default-session',
        userMessage: message,
        currentAgentId: options.currentAgentId,
        requestedCapabilities: options.requestedCapabilities,
        urgencyLevel: options.urgencyLevel || 'medium',
        patientContext: options.patientContext
      });

      if (orchestrationResponse) {
        const {
          agentResponse,
          handoffOccurred,
          previousAgent,
          nextRecommendedAgent,
          unifiedContext,
          contextualInsights,
          performanceMetrics
        } = orchestrationResponse;

        console.log('✅ Agent response with advanced context generated successfully', {
          agent: agentResponse.agentName,
          confidence: agentResponse.confidence,
          handoffOccurred,
          contextualInsights: contextualInsights?.length || 0,
          riskLevel: unifiedContext?.synthesizedContext?.riskAssessment?.overallRiskLevel,
          performanceScore: performanceMetrics?.overallPerformance
        });

        return {
          success: true,
          data: {
            content: agentResponse.content,
            agentId: agentResponse.agentId,
            agentName: agentResponse.agentName,
            confidence: agentResponse.confidence,
            reasoning: agentResponse.reasoning,
            handoffOccurred,
            previousAgent,
            nextRecommendedAgent,
            suggestedHandoffs: agentResponse.suggestedHandoffs,
            emergencyFlags: agentResponse.emergencyFlags,
            followUpActions: agentResponse.followUpActions,
            metadata: agentResponse.metadata,
            // Advanced context data
            unifiedContext,
            contextualInsights,
            performanceMetrics,
            riskAssessment: unifiedContext?.synthesizedContext?.riskAssessment,
            recommendations: unifiedContext?.contextualRecommendations,
            patientProfile: unifiedContext?.patientProfile?.profile
          }
        };
      } else {
        throw new Error('No response from agent orchestrator');
      }
    } catch (err) {
      console.error('❌ Agent Orchestrator Error:', err);
      setError(err.message);

      // Return error response
      return {
        success: false,
        error: err.message || 'Agent orchestrator failed'
      };
    } finally {
      setIsLoading(false);
    }
  }, []);

  // Helper function to get agent specialty areas
  const getAgentSpecialtyAreas = (role) => {
    const specialtyMap = {
      'triage': ['Initial Assessment', 'Symptom Evaluation', 'Urgency Determination'],
      'emergency': ['Critical Care', 'Emergency Response', 'Life-Threatening Situations'],
      'general_practitioner': ['Primary Care', 'General Medicine', 'Preventive Care', 'Chronic Disease Management'],
      'cardiologist': ['Heart Disease', 'Cardiovascular Health', 'Cardiac Emergencies', 'Hypertension'],
      'nutritionist': ['Dietary Planning', 'Nutrition Therapy', 'Weight Management', 'Metabolic Health'],
      'psychiatrist': ['Mental Health', 'Depression', 'Anxiety', 'Crisis Intervention', 'Therapy']
    };
    return specialtyMap[role] || ['General Healthcare'];
  };

  // Get available medical agents from the real agent registry with enhanced data
  const getAvailableAgents = useCallback(() => {
    try {
      const allAgents = agentRegistry.getAllAgents();
      return allAgents.map(agent => {
        const metrics = agent.getPerformanceMetrics();
        return {
          id: agent.id,
          name: agent.name,
          specialization: agent.role.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase()),
          description: `${agent.role.replace('_', ' ')} specialist with capabilities: ${agent.capabilities.join(', ')}`,
          role: agent.role,
          capabilities: agent.capabilities,
          isActive: agent.isActive,
          confidence: metrics.averageConfidence || 0.8,
          totalInteractions: metrics.totalInteractions || 0,
          successRate: metrics.successRate || 0.9,
          averageResponseTime: metrics.averageResponseTime || 1500,
          specialtyAreas: getAgentSpecialtyAreas(agent.role)
        };
      });
    } catch (error) {
      console.error('Error getting available agents:', error);
      // Fallback to basic agent list if registry fails
      return [
        {
          id: 'fallback-gp',
          name: 'General Practitioner',
          specialization: 'General Medicine',
          description: 'Primary care physician for general health consultations',
          role: 'general_practitioner',
          capabilities: ['primary_care'],
          isActive: true,
          confidence: 0.8
        }
      ];
    }
  }, []);

  // Get patient context and insights for current session
  const getPatientContext = useCallback(async (sessionId) => {
    try {
      // This would typically call the patient context aggregator
      // For now, return a placeholder that indicates the feature is available
      return {
        sessionId,
        hasAdvancedContext: true,
        contextAvailable: true,
        message: 'Advanced patient context available through agent responses'
      };
    } catch (error) {
      console.error('❌ Failed to get patient context:', error);
      return null;
    }
  }, []);

  // Get conversation insights and recommendations
  const getConversationInsights = useCallback(async (sessionId) => {
    try {
      // This would typically call the conversation context manager
      return {
        sessionId,
        hasConversationInsights: true,
        insightsAvailable: true,
        message: 'Conversation insights available through agent responses'
      };
    } catch (error) {
      console.error('❌ Failed to get conversation insights:', error);
      return null;
    }
  }, []);

  // Get predictive health insights
  const getPredictiveInsights = useCallback(async (sessionId) => {
    try {
      // This would typically call the predictive analytics service
      return {
        sessionId,
        hasPredictiveInsights: true,
        predictionsAvailable: true,
        message: 'Predictive insights available through agent responses'
      };
    } catch (error) {
      console.error('❌ Failed to get predictive insights:', error);
      return null;
    }
  }, []);

  // Get real-time agent collaboration status
  const getCollaborationStatus = useCallback((sessionId) => {
    try {
      const stats = agentOrchestrator.getOrchestratorStats();
      return {
        sessionId,
        activeAgents: stats.activeAgents || 0,
        collaborationActive: stats.activeSessions > 0,
        multiAgentCapable: true,
        realTimeEnabled: true
      };
    } catch (error) {
      console.error('❌ Failed to get collaboration status:', error);
      return null;
    }
  }, []);

  return {
    // State
    isInitialized,
    isLoading,
    error,
    providers,
    healthStatus,

    // Actions
    generateResponse,
    sendMessageToAgent, // Enhanced multi-agent messaging with advanced context
    selectProvider,
    getProviderStats,
    getAvailableAgents,
    clearError,

    // Advanced Context Functions
    getPatientContext,
    getConversationInsights,
    getPredictiveInsights,
    getCollaborationStatus,

    // Agent System Access
    agentOrchestrator,
    agentRegistry,

    // Computed
    isHealthy: isInitialized && Object.values(healthStatus).some(status => status === 'healthy'),
    availableProviders: providers.filter(p => healthStatus[p] === 'healthy')
  };
}
