import React, { useState, useEffect } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import Icon from '../../components/AppIcon';
import Button from '../../components/ui/Button';
import Header from '../../components/ui/Header';
import ConsultationStatusIndicator from '../../components/ui/ConsultationStatusIndicator';
import PaymentModal from '../../components/ui/PaymentModal';
import VoiceActivationButton from './components/VoiceActivationButton';
import RealTimeTranscription from './components/RealTimeTranscription';
import AudioPlaybackControls from './components/AudioPlaybackControls';
import ConversationHistorySidebar from './components/ConversationHistorySidebar';
import MultiAgentStatus from './components/MultiAgentStatus';
import ActiveAgentIndicator from './components/ActiveAgentIndicator';
import SessionProgressIndicator from './components/SessionProgressIndicator';
import QuickActionControls from './components/QuickActionControls';
import { useAIOrchestrator } from '../../hooks/useAIOrchestrator';
import { useSpeechEngine } from '../../hooks/useSpeechEngine';
import speechToTextService from '../../services/speechToTextService';
import textToSpeechService from '../../services/textToSpeechService';
import { usePayment } from '../../contexts/PaymentContext';
import { useAuth } from '../../contexts/AuthContext';

const VoiceConsultationInterface = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const { user } = useAuth();
  const { 
    checkConsultationEligibility, 
    hasActiveSubscription, 
    getRemainingCredits,
    payForConsultation 
  } = usePayment();

  // Initialize AI orchestrator and speech engine
  const aiOrchestrator = useAIOrchestrator();
  const speechEngine = useSpeechEngine();

  const [isSessionActive, setIsSessionActive] = useState(false);
  const [currentPhase, setCurrentPhase] = useState('ready'); // 'ready', 'listening', 'processing', 'responding'
  const [sessionProgress, setSessionProgress] = useState(0);
  const [isLoading, setIsLoading] = useState(true);
  const [showPaymentModal, setShowPaymentModal] = useState(false);
  const [paymentRequired, setPaymentRequired] = useState(false);
  const [consultationFee] = useState(2000); // NGN 2000 per consultation
  const [eligibilityStatus, setEligibilityStatus] = useState(null);

  // Session data
  const [sessionData, setSessionData] = useState({
    id: null,
    startTime: null,
    duration: 0,
    transcriptMessages: [],
    activeAgents: [],
    conversationHistory: []
  });

  // Audio states
  const [isRecording, setIsRecording] = useState(false);
  const [audioLevel, setAudioLevel] = useState(0);
  const [isPlayingResponse, setIsPlayingResponse] = useState(false);
  const [mediaRecorder, setMediaRecorder] = useState(null);
  const [audioStream, setAudioStream] = useState(null);
  const [transcriptionText, setTranscriptionText] = useState('');
  const [isProcessingAudio, setIsProcessingAudio] = useState(false);

  // UI states
  const [showConversationHistory, setShowConversationHistory] = useState(false);
  const [selectedAgent, setSelectedAgent] = useState(null);

  // Cleanup effect for audio resources
  useEffect(() => {
    return () => {
      // Cleanup on component unmount
      if (audioStream) {
        audioStream.getTracks().forEach(track => track.stop());
      }
      if (mediaRecorder && mediaRecorder.state === 'recording') {
        mediaRecorder.stop();
      }
    };
  }, [audioStream, mediaRecorder]);

  // Mock active agents
  const activeAgents = [
    {
      id: 'agent_1',
      name: 'Dr. Sarah Chen',
      specialty: 'General Practitioner',
      avatar: 'https://images.unsplash.com/photo-1559839734-2b71ea197ec2?w=400&h=400&fit=crop&crop=face',
      isActive: true,
      confidence: 95,
      voiceProfile: 'Professional Female, American'
    },
    {
      id: 'agent_2',
      name: 'Dr. Michael Rodriguez',
      specialty: 'Cardiologist',
      avatar: 'https://images.unsplash.com/photo-1612349317150-e413f6a5b16d?w=400&h=400&fit=crop&crop=face',
      isActive: false,
      confidence: 92,
      voiceProfile: 'Warm Male, Spanish'
    }
  ];

  // Resume session from navigation state
  const resumeSessionId = location.state?.resumeSessionId;

  useEffect(() => {
    if (!user) {
      navigate('/authentication-demo-access');
      return;
    }

    initializeConsultation();
  }, [user, navigate]);

  useEffect(() => {
    // Session timer
    let interval;
    if (isSessionActive) {
      interval = setInterval(() => {
        setSessionData(prev => ({
          ...prev,
          duration: prev.duration + 1
        }));
      }, 1000);
    }
    return () => clearInterval(interval);
  }, [isSessionActive]);

  const initializeConsultation = async () => {
    try {
      setIsLoading(true);

      // Check if user can start consultation with testing override
      let eligibilityResult;

      // TESTING MODE: Check environment or user preference for testing override
      // Default to testing mode in development
      if (process.env.NODE_ENV === 'development' && !localStorage.getItem('voicehealth_testing_mode')) {
        localStorage.setItem('voicehealth_testing_mode', 'true');
      }

      const isTestingMode = process.env.NODE_ENV === 'development' ||
                           localStorage.getItem('voicehealth_testing_mode') === 'true';

      if (isTestingMode) {
        console.log('✅ Voice Consultation - Testing mode enabled, bypassing payment checks');
        eligibilityResult = {
          success: true,
          canStart: true,
          reason: 'testing_mode',
          creditsRemaining: 999,
          subscriptionActive: true,
          testingMode: true
        };
      } else {
        // Real payment eligibility check
        eligibilityResult = await checkConsultationEligibility();

        if (!eligibilityResult.success) {
          console.error('Eligibility check failed:', eligibilityResult.error);
          setEligibilityStatus({ canStart: false, reason: 'error' });
          return;
        }
      }

      setEligibilityStatus(eligibilityResult);

      // If user can't start consultation, show payment options (unless testing mode)
      if (!eligibilityResult.canStart && !isTestingMode) {
        if (eligibilityResult.reason === 'no_subscription') {
          // Redirect to payment plans for subscription
          navigate('/payment-plans');
          return;
        } else if (eligibilityResult.reason === 'no_credits') {
          // Show payment modal for per-consultation payment
          setPaymentRequired(true);
        }
      } else {
        setPaymentRequired(false); // Disable payment requirement for testing or valid subscription
      }

      // If resuming session, load session data
      if (resumeSessionId) {
        await loadSessionData(resumeSessionId);
      }
    } catch (error) {
      console.error('Consultation initialization error:', error);
      setEligibilityStatus({ canStart: false, reason: 'error' });
    } finally {
      setIsLoading(false);
    }
  };

  const loadSessionData = async (sessionId) => {
    // Mock loading session data
    setSessionData(prev => ({
      ...prev,
      id: sessionId,
      conversationHistory: [
        {
          id: 1,
          speaker: 'user',
          content: 'Hello, I have been experiencing some chest discomfort lately.',
          timestamp: new Date(Date.now() - 300000),
          confidence: 96
        },
        {
          id: 2,
          speaker: 'Dr. Sarah Chen',
          content: 'I understand your concern about chest discomfort. Can you describe when you typically experience this discomfort?',
          timestamp: new Date(Date.now() - 240000),
          confidence: 98
        }
      ]
    }));
    setIsSessionActive(true);
    setSessionProgress(35);
  };

  const handleStartConsultation = async () => {
    console.log('🚀 Starting consultation...', { isSessionActive, paymentRequired });
    
    // TEMPORARILY DISABLED FOR TESTING - Payment requirement bypassed
    // if (paymentRequired && !hasActiveSubscription()) {
    //   setShowPaymentModal(true);
    //   return;
    // }

    // Start new consultation session
    const newSessionId = `session_${Date.now()}`;
    setSessionData(prev => ({
      ...prev,
      id: newSessionId,
      startTime: new Date(),
      activeAgents: [activeAgents[0]]
    }));

    setIsSessionActive(true);
    setCurrentPhase('listening');
    setSelectedAgent(activeAgents[0]);
    
    console.log('✅ Consultation started successfully', { sessionId: newSessionId, agent: activeAgents[0]?.name });
  };

  const handlePaymentSuccess = () => {
    setShowPaymentModal(false);
    setPaymentRequired(false);
    handleStartConsultation();
  };

  const handleVoiceActivation = async () => {
    console.log('🎤 Voice activation triggered', { isSessionActive, isRecording });
    
    if (!isSessionActive) {
      console.log('❌ Session not active, voice activation blocked');
      return;
    }

    if (!isRecording) {
      // Start recording
      try {
        const stream = await navigator.mediaDevices.getUserMedia({ 
          audio: {
            echoCancellation: true,
            noiseSuppression: true,
            autoGainControl: true,
            sampleRate: 44100
          } 
        });
        
        setAudioStream(stream);
        
        // Try different codec options for better compatibility
        let mimeType = 'audio/webm;codecs=opus';
        if (!MediaRecorder.isTypeSupported(mimeType)) {
          mimeType = 'audio/webm';
          if (!MediaRecorder.isTypeSupported(mimeType)) {
            mimeType = 'audio/mp4';
            if (!MediaRecorder.isTypeSupported(mimeType)) {
              mimeType = ''; // Use default
            }
          }
        }
        
        const recorder = new MediaRecorder(stream, mimeType ? { mimeType } : {});
        
        const audioChunks = [];
        
        recorder.ondataavailable = (event) => {
          audioChunks.push(event.data);
        };
        
        recorder.onstop = async () => {
          const audioBlob = new Blob(audioChunks, { type: mimeType || 'audio/webm' });
          await processAudioRecording(audioBlob);
        };
        
        // Start audio level monitoring
        const audioContext = new AudioContext();
        const analyser = audioContext.createAnalyser();
        const microphone = audioContext.createMediaStreamSource(stream);
        microphone.connect(analyser);
        
        analyser.fftSize = 256;
        const bufferLength = analyser.frequencyBinCount;
        const dataArray = new Uint8Array(bufferLength);
        
        let isMonitoring = true;
        const monitorAudioLevel = () => {
          if (isMonitoring && stream.active) {
            analyser.getByteFrequencyData(dataArray);
            const average = dataArray.reduce((a, b) => a + b) / bufferLength;
            setAudioLevel(average);
            requestAnimationFrame(monitorAudioLevel);
          }
        };
        
        // Store monitoring control in a way that can be accessed later
        stream.stopMonitoring = () => { isMonitoring = false; };
        
        setMediaRecorder(recorder);
        setIsRecording(true);
        setCurrentPhase('listening');
        setTranscriptionText('Listening...');
        
        console.log('🎙️ Starting MediaRecorder with mimeType:', mimeType);
        recorder.start();
        monitorAudioLevel();
        console.log('✅ Voice recording started successfully');
        
      } catch (error) {
        console.error('❌ Error accessing microphone:', error);
        alert('Please allow microphone access to start voice consultation. Error: ' + error.message);
      }
    } else {
      // Stop recording
      if (mediaRecorder && mediaRecorder.state === 'recording') {
        mediaRecorder.stop();
        setIsRecording(false);
        setCurrentPhase('processing');
        setTranscriptionText('Processing your audio...');
        setIsProcessingAudio(true);
        
        // Stop audio stream and monitoring
        if (audioStream) {
          if (audioStream.stopMonitoring) {
            audioStream.stopMonitoring();
          }
          audioStream.getTracks().forEach(track => track.stop());
        }
      }
    }
  };

  const processAudioRecording = async (audioBlob) => {
    if (!audioBlob) return;
    
    try {
      setIsProcessingAudio(true);
      setCurrentPhase('processing');
      console.log('🎵 Processing audio recording...', { size: audioBlob.size, type: audioBlob.type });
      
      // Use real Whisper API for transcription
      const transcriptionResult = await speechToTextService.transcribeAudio(audioBlob, {
        language: 'en' // Can be made dynamic based on user preference
      });
      
      const transcribedText = transcriptionResult.text;
      console.log('📝 Transcribed text:', transcribedText, {
        confidence: transcriptionResult.confidence,
        isMock: transcriptionResult.isMock
      });
      
      setTranscriptionText(transcribedText);
      
      // Add user message to conversation
      addTranscriptMessage('user', transcribedText);
      
      // Process AI response
      console.log('🤖 Generating AI response...');
      await generateAIResponse(transcribedText);
      
    } catch (error) {
      console.error('❌ Error processing audio:', error);
      setTranscriptionText('Error processing audio. Please try again.');
      
      // Show user-friendly error message
      addTranscriptMessage('system', 'Sorry, I had trouble understanding that. Please try speaking again.');
    } finally {
      setIsProcessingAudio(false);
    }
  };
  
  const generateAIResponse = async (userInput) => {
    try {
      setCurrentPhase('responding');
      setIsPlayingResponse(true);
      
      console.log('🤖 Generating AI response with orchestrator...', { input: userInput.substring(0, 50) + '...' });
      
      // Prepare medical context and conversation history
      const conversationMessages = [
        {
          role: 'system',
          content: `You are a professional AI medical assistant providing health consultations. 
          Current agent: ${selectedAgent?.name || 'General Practitioner'}
          Specialization: ${selectedAgent?.specialization || 'General Medicine'}
          
          Guidelines:
          - Provide empathetic, professional medical guidance
          - Ask relevant follow-up questions
          - Recommend seeking in-person care when appropriate
          - Never provide definitive diagnoses without proper examination
          - Keep responses concise and conversational
          - Focus on the patient's immediate concerns`
        },
        ...sessionData.transcriptMessages.slice(-6).map(msg => ({
          role: msg.speaker === 'You' ? 'user' : 'assistant',
          content: msg.content
        })),
        {
          role: 'user',
          content: userInput
        }
      ];
      
      // Use AI orchestrator to generate response
      const aiResponse = await aiOrchestrator.generateResponse({
        messages: conversationMessages,
        agentType: selectedAgent?.id || 'general-practitioner',
        complexity: 'medium',
        options: {
          maxTokens: 200,
          temperature: 0.7
        }
      });
      
      const responseText = aiResponse.content;
      console.log('✅ AI response generated:', responseText.substring(0, 100) + '...');
      
      // Add AI agent response to conversation
      addTranscriptMessage('agent', responseText);
      
      // Convert response to speech and play
      console.log('🔊 Synthesizing speech...');
      await synthesizeAndPlayResponse(responseText);
      
      // Update session progress
      setSessionProgress(prev => Math.min(prev + 15, 100));
      
    } catch (error) {
      console.error('❌ Error generating AI response:', error);
      
      // Fallback response for errors
      const fallbackResponse = "I apologize, I'm having some technical difficulties. Could you please repeat your question?";
      addTranscriptMessage('agent', fallbackResponse);
      await synthesizeAndPlayResponse(fallbackResponse);
    } finally {
      setCurrentPhase('listening');
      setIsPlayingResponse(false);
    }
  };
  
  const synthesizeAndPlayResponse = async (text) => {
    try {
      console.log('🎤 Synthesizing speech for:', text.substring(0, 50) + '...');
      
      // Use real TTS service to synthesize speech
      const speechResult = await textToSpeechService.synthesizeSpeech(text, {
        agentType: selectedAgent?.id || 'general-practitioner',
        stability: 0.6,
        similarity_boost: 0.8
      });
      
      console.log('✅ Speech synthesis completed, playing audio...');
      
      // Play the synthesized audio
      await textToSpeechService.playAudio(speechResult.audioUrl, {
        volume: 0.8,
        speed: 1.0
      });
      
      console.log('🎵 Audio playback completed');
      
    } catch (error) {
      console.error('❌ TTS error:', error);
      
      // Fallback: simulate audio playback timing
      const readingTime = Math.max(2000, text.length * 50);
      await new Promise(resolve => setTimeout(resolve, readingTime));
    }
  };

  const addTranscriptMessage = (speaker, content) => {
    const newMessage = {
      id: Date.now(),
      speaker: speaker === 'agent' ? selectedAgent?.name || 'AI Agent' : 'You',
      content,
      timestamp: new Date(),
      confidence: Math.floor(Math.random() * 10) + 90
    };
    
    setSessionData(prev => ({
      ...prev,
      transcriptMessages: [...prev.transcriptMessages, newMessage],
      conversationHistory: [...prev.conversationHistory, newMessage]
    }));
  };

  const handleEmergencyStop = () => {
    setIsSessionActive(false);
    setIsRecording(false);
    setCurrentPhase('ready');
    setSessionProgress(0);
    // In real implementation, this would save the session and notify healthcare providers
  };

  const handleEndSession = () => {
    setIsSessionActive(false);
    setCurrentPhase('ready');
    // Navigate to session summary or dashboard
    navigate('/session-dashboard-history');
  };

  const formatDuration = (seconds) => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
  };

  if (!user) {
    return null;
  }

  if (isLoading) {
    return (
      <div className="min-h-screen bg-background">
        <Header />
        <div className="flex items-center justify-center min-h-[80vh]">
          <div className="text-center">
            <div className="w-12 h-12 bg-primary-50 rounded-full flex items-center justify-center mb-4 mx-auto animate-breathe">
              <Icon name="Activity" size={24} color="var(--color-primary)" />
            </div>
            <p className="text-text-secondary">Initializing consultation...</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-background">
      <Header />
      
      {/* Session Status Indicator */}
      {isSessionActive && (
        <ConsultationStatusIndicator
          isActive={isSessionActive}
          activeAgents={sessionData.activeAgents}
          sessionProgress={sessionProgress}
          consultationPhase={currentPhase}
          onEmergencyStop={handleEmergencyStop}
        />
      )}

      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pt-20 pb-12">
        <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
          {/* Main Consultation Area */}
          <div className="lg:col-span-3">
            {/* Session Header */}
            <div className="bg-surface rounded-xl shadow-minimal border border-border p-6 mb-6">
              <div className="flex items-center justify-between mb-4">
                <div>
                  <h1 className="text-2xl font-bold text-text-primary font-heading">
                    {isSessionActive ? 'Active Consultation' : 'Voice Consultation'}
                  </h1>
                  <p className="text-text-secondary mt-1">
                    {isSessionActive
                      ? `Session Duration: ${formatDuration(sessionData.duration)}`
                      : localStorage.getItem('voicehealth_testing_mode') === 'true'
                        ? 'Ready to start your AI-powered health consultation - Testing Mode'
                        : 'Ready to start your AI-powered health consultation'
                    }
                  </p>

                  {/* Testing Mode Toggle - Development Only */}
                  {process.env.NODE_ENV === 'development' && (
                    <div className="mt-2">
                      <button
                        onClick={() => {
                          const isTestingMode = localStorage.getItem('voicehealth_testing_mode') === 'true';
                          localStorage.setItem('voicehealth_testing_mode', (!isTestingMode).toString());
                          window.location.reload();
                        }}
                        className="text-xs px-2 py-1 bg-blue-100 text-blue-800 rounded hover:bg-blue-200"
                      >
                        {localStorage.getItem('voicehealth_testing_mode') === 'true'
                          ? '🧪 Testing Mode ON'
                          : '💳 Payment Mode ON'
                        }
                      </button>
                    </div>
                  )}
                </div>
                
                {isSessionActive && (
                  <div className="flex items-center space-x-3">
                    <SessionProgressIndicator progress={sessionProgress} />
                    <Button
                      variant="outline"
                      onClick={handleEndSession}
                      iconName="Square"
                      iconPosition="left"
                      size="sm"
                    >
                      End Session
                    </Button>
                  </div>
                )}
              </div>

              {/* Payment Warning - Shown only in production mode */}
              {paymentRequired && !hasActiveSubscription() &&
               localStorage.getItem('voicehealth_testing_mode') !== 'true' && (
                <div className="bg-orange-50 border border-orange-200 rounded-lg p-4 mb-4">
                  <div className="flex items-start">
                    <Icon name="AlertCircle" size={20} className="text-orange-600 mt-0.5 mr-3" />
                    <div>
                      <h3 className="text-sm font-medium text-orange-800 mb-1">
                        Payment Required
                      </h3>
                      <p className="text-sm text-orange-700 mb-2">
                        You need to pay for this consultation or subscribe to a plan to continue.
                      </p>
                      <div className="flex space-x-3">
                        <Button
                          size="sm"
                          onClick={() => setShowPaymentModal(true)}
                        >
                          Pay ₦{consultationFee.toLocaleString()}
                        </Button>
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => navigate('/payment-plans')}
                        >
                          View Plans
                        </Button>
                      </div>
                    </div>
                  </div>
                </div>
              )}

              {/* Subscription Status */}
              {hasActiveSubscription() && (
                <div className="bg-green-50 border border-green-200 rounded-lg p-4 mb-4">
                  <div className="flex items-center">
                    <Icon name="CheckCircle" size={16} className="text-green-600 mr-2" />
                    <span className="text-sm text-green-800">
                      {getRemainingCredits() === Infinity 
                        ? 'Unlimited consultations available' 
                        : `${getRemainingCredits()} consultations remaining`
                      }
                    </span>
                  </div>
                </div>
              )}

              {/* Agent Status */}
              {isSessionActive && selectedAgent && (
                <div className="flex items-center justify-between">
                  <ActiveAgentIndicator 
                    agent={selectedAgent}
                    isActive={currentPhase !== 'ready'}
                    className="mb-0"
                  />
                  
                  <MultiAgentStatus 
                    agents={activeAgents}
                    onAgentToggle={(agentId) => {
                      console.log('Toggle agent:', agentId);
                    }}
                  />
                </div>
              )}
            </div>

            {/* Voice Activation Area */}
            <div className="bg-surface rounded-xl shadow-minimal border border-border p-8 mb-6 text-center">
              <VoiceActivationButton
                isListening={isRecording}
                isProcessing={isProcessingAudio || currentPhase === 'processing'}
                onStartListening={handleVoiceActivation}
                onStopListening={handleVoiceActivation}
                disabled={!isSessionActive}
              />

              {!isSessionActive && (
                <div className="mt-6">
                  <Button
                    onClick={handleStartConsultation}
                    size="lg"
                    disabled={false}
                    iconName="Play"
                    iconPosition="left"
                  >
                    Start Consultation
                  </Button>
                </div>
              )}

              {/* Phase Indicator */}
              <div className="mt-4">
                <p className="text-sm text-text-secondary">
                  {currentPhase === 'ready' && 'Ready to listen'}
                  {currentPhase === 'listening' && 'Listening...'}
                  {currentPhase === 'processing' && 'Processing your input...'}
                  {currentPhase === 'responding' && 'Agent is responding...'}
                </p>
              </div>
            </div>

            {/* Real-time Transcription */}
            {isSessionActive && (
              <RealTimeTranscription
                transcriptionData={sessionData.transcriptMessages}
                isListening={isRecording}
                isProcessing={isProcessingAudio}
                onClearTranscription={() => {
                  setSessionData(prev => ({ ...prev, transcriptMessages: [] }));
                }}
                className="mb-6"
              />
            )}
            
            {/* Live Transcription Display */}
            {isSessionActive && transcriptionText && (
              <div className="bg-secondary-50 border border-secondary-200 rounded-lg p-4 mb-6">
                <div className="flex items-start space-x-3">
                  <Icon 
                    name={isProcessingAudio ? "Loader" : isRecording ? "Mic" : "MessageSquare"} 
                    size={20} 
                    className={`mt-0.5 ${
                      isProcessingAudio ? 'animate-spin text-primary-600' :
                      isRecording ? 'text-red-500 animate-pulse' : 'text-secondary-600'
                    }`} 
                  />
                  <div className="flex-1">
                    <h3 className="text-sm font-medium text-text-primary mb-1">
                      {isProcessingAudio ? 'Processing Audio...' :
                       isRecording ? 'Listening...' : 'Transcription'}
                    </h3>
                    <p className="text-sm text-text-secondary">
                      {transcriptionText}
                    </p>
                  </div>
                </div>
              </div>
            )}

            {/* Audio Playback Controls */}
            {isSessionActive && (
              <AudioPlaybackControls
                isPlaying={isPlayingResponse}
                onPlayPause={() => setIsPlayingResponse(!isPlayingResponse)}
                onRewind={() => console.log('Rewind')}
                onForward={() => console.log('Forward')}
                className="mb-6"
              />
            )}

            {/* Quick Actions */}
            {isSessionActive && (
              <QuickActionControls
                onRequestSpecialist={() => console.log('Request specialist')}
                onTakeNotes={() => console.log('Take notes')}
                onScheduleFollowup={() => console.log('Schedule followup')}
                onEmergencyAlert={handleEmergencyStop}
              />
            )}
          </div>

          {/* Conversation History Sidebar */}
          <div className="lg:col-span-1">
            <ConversationHistorySidebar
              conversationHistory={sessionData.conversationHistory}
              isVisible={showConversationHistory}
              onToggleVisibility={() => setShowConversationHistory(!showConversationHistory)}
              onClearHistory={() => {
                setSessionData(prev => ({
                  ...prev,
                  conversationHistory: [],
                  transcriptMessages: []
                }));
              }}
            />
          </div>
        </div>
      </main>

      {/* Payment Modal */}
      <PaymentModal
        isOpen={showPaymentModal}
        onClose={() => setShowPaymentModal(false)}
        paymentType="consultation"
        consultationId={sessionData.id}
        amount={consultationFee}
        currency="NGN"
        planDetails={{
          name: 'Single Consultation',
          description: 'Pay for individual consultation session',
          features: ['AI-powered consultation', 'Real-time transcription', 'Session recording']
        }}
      />
    </div>
  );
};

export default VoiceConsultationInterface;