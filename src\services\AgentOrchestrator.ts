/**
 * AGENT ORCHESTRATOR SERVICE
 * 
 * Coordinates interactions between multiple AI agents, manages agent handoffs,
 * and provides a unified interface for the multi-agent consultation system.
 * Replaces the hardcoded agent management with dynamic agent coordination.
 * 
 * FEATURES:
 * - Dynamic agent selection and routing
 * - Agent handoff management
 * - Multi-agent collaboration
 * - Context transfer between agents
 * - Performance monitoring and optimization
 * - Emergency protocol coordination
 */

import { agentRegistry, type AgentRegistry } from './AgentRegistry';
import { memoryManager, type MemoryManager } from './MemoryManager';
import type { 
  IAgent, 
  AgentRequest, 
  AgentResponse, 
  AgentRole,
  AgentCapability,
  AgentHandoffSuggestion
} from '../agents/BaseAgent';

// Import concrete agent implementations
import GeneralPractitionerAgent from '../agents/GeneralPractitionerAgent';
import EmergencyAgent from '../agents/EmergencyAgent';
import TriageAgent from '../agents/TriageAgent';
import CardiologistAgent from '../agents/CardiologistAgent';
import NutritionistAgent from '../agents/NutritionistAgent';
import MentalHealthAgent from '../agents/MentalHealthAgent';

export interface OrchestrationRequest {
  sessionId: string;
  userMessage: string;
  currentAgentId?: string;
  requestedCapabilities?: AgentCapability[];
  urgencyLevel?: 'low' | 'medium' | 'high' | 'critical';
  patientContext?: any;
}

export interface OrchestrationResponse {
  agentResponse: AgentResponse;
  handoffOccurred: boolean;
  previousAgent?: string;
  nextRecommendedAgent?: string;
  collaboratingAgents?: string[];
  sessionContext: any;
}

export interface AgentHandoffContext {
  fromAgent: IAgent;
  toAgent: IAgent;
  reason: string;
  contextSummary: string;
  patientConsent: boolean;
  handoffTime: string;
}

export class AgentOrchestrator {
  private registry: AgentRegistry;
  private memory: MemoryManager;
  private activeAgents: Map<string, string> = new Map(); // sessionId -> agentId
  private handoffHistory: Map<string, AgentHandoffContext[]> = new Map(); // sessionId -> handoffs

  constructor() {
    this.registry = agentRegistry;
    this.memory = memoryManager;
    
    console.log('🎭 Initializing Agent Orchestrator...');
  }

  /**
   * Initialize the orchestrator and register all agents
   */
  async initialize(): Promise<void> {
    try {
      console.log('🚀 Starting Agent Orchestrator initialization...');

      // Register core agents
      await this.registerCoreAgents();

      // Verify all agents are healthy
      const stats = this.registry.getRegistryStats();
      console.log(`✅ Agent Orchestrator initialized with ${stats.totalAgents} agents (${stats.healthyAgents} healthy)`);

    } catch (error) {
      console.error('❌ Failed to initialize Agent Orchestrator:', error);
      throw error;
    }
  }

  /**
   * Process a user request through the agent system
   */
  async processRequest(request: OrchestrationRequest): Promise<OrchestrationResponse> {
    const startTime = Date.now();
    
    try {
      console.log(`🎯 Processing request for session: ${request.sessionId}`);

      // Get conversation history for context
      const conversationHistory = await this.memory.getConversationHistory(request.sessionId);

      // Determine which agent should handle this request
      const selectedAgent = await this.selectAgent(request, conversationHistory);
      
      if (!selectedAgent) {
        throw new Error('No suitable agent found for request');
      }

      // Check if this is a handoff from another agent
      const currentAgentId = this.activeAgents.get(request.sessionId);
      const handoffOccurred = currentAgentId && currentAgentId !== selectedAgent.id;

      if (handoffOccurred) {
        await this.handleAgentHandoff(request.sessionId, currentAgentId!, selectedAgent.id, 'System-initiated handoff');
      }

      // Update active agent for session
      this.activeAgents.set(request.sessionId, selectedAgent.id);

      // Create agent request
      const agentRequest: AgentRequest = {
        sessionId: request.sessionId,
        userMessage: request.userMessage,
        conversationHistory,
        patientContext: request.patientContext,
        urgencyLevel: request.urgencyLevel,
        requestedCapabilities: request.requestedCapabilities
      };

      // Get response from selected agent
      const agentResponse = await selectedAgent.handleMessage(agentRequest);

      // Save the response to memory
      await this.saveAgentResponse(request.sessionId, agentResponse);

      // Check if agent suggests handoffs
      let nextRecommendedAgent: string | undefined;
      if (agentResponse.suggestedHandoffs && agentResponse.suggestedHandoffs.length > 0) {
        const primaryHandoff = agentResponse.suggestedHandoffs[0];
        const targetAgent = this.registry.getAgentsByRole(primaryHandoff.targetAgentRole)[0];
        if (targetAgent) {
          nextRecommendedAgent = targetAgent.id;
        }
      }

      // Get session context
      const sessionContext = await this.memory.getConversationContext(request.sessionId);

      const processingTime = Date.now() - startTime;
      console.log(`✅ Request processed in ${processingTime}ms by agent: ${selectedAgent.name}`);

      return {
        agentResponse,
        handoffOccurred,
        previousAgent: handoffOccurred ? currentAgentId : undefined,
        nextRecommendedAgent,
        collaboratingAgents: [], // TODO: Implement multi-agent collaboration
        sessionContext
      };

    } catch (error) {
      console.error('❌ Error processing request:', error);
      throw error;
    }
  }

  /**
   * Perform agent handoff
   */
  async performHandoff(
    sessionId: string, 
    targetAgentRole: AgentRole, 
    reason: string,
    patientConsent: boolean = true
  ): Promise<boolean> {
    try {
      console.log(`🔄 Performing handoff for session ${sessionId} to ${targetAgentRole}`);

      const currentAgentId = this.activeAgents.get(sessionId);
      if (!currentAgentId) {
        console.warn('⚠️ No current agent found for handoff');
        return false;
      }

      const targetAgents = this.registry.getAgentsByRole(targetAgentRole);
      if (targetAgents.length === 0) {
        console.warn(`⚠️ No agents found for role: ${targetAgentRole}`);
        return false;
      }

      const targetAgent = targetAgents[0]; // Select first available agent
      
      await this.handleAgentHandoff(sessionId, currentAgentId, targetAgent.id, reason, patientConsent);
      
      // Update active agent
      this.activeAgents.set(sessionId, targetAgent.id);

      console.log(`✅ Handoff completed: ${currentAgentId} → ${targetAgent.id}`);
      return true;

    } catch (error) {
      console.error('❌ Handoff failed:', error);
      return false;
    }
  }

  /**
   * Get current agent for a session
   */
  getCurrentAgent(sessionId: string): IAgent | null {
    const agentId = this.activeAgents.get(sessionId);
    return agentId ? this.registry.getAgent(agentId) : null;
  }

  /**
   * Get handoff history for a session
   */
  getHandoffHistory(sessionId: string): AgentHandoffContext[] {
    return this.handoffHistory.get(sessionId) || [];
  }

  /**
   * Get orchestrator statistics
   */
  getOrchestratorStats(): {
    activeSessions: number;
    totalHandoffs: number;
    agentUtilization: Record<string, number>;
    registryStats: any;
  } {
    const totalHandoffs = Array.from(this.handoffHistory.values())
      .reduce((sum, handoffs) => sum + handoffs.length, 0);

    const agentUtilization: Record<string, number> = {};
    this.activeAgents.forEach(agentId => {
      agentUtilization[agentId] = (agentUtilization[agentId] || 0) + 1;
    });

    return {
      activeSessions: this.activeAgents.size,
      totalHandoffs,
      agentUtilization,
      registryStats: this.registry.getRegistryStats()
    };
  }

  /**
   * Register core agents in the system
   */
  private async registerCoreAgents(): Promise<void> {
    console.log('📝 Registering core agents...');

    // Register Triage Agent (highest priority - first contact)
    const triageAgent = new TriageAgent(this.memory);
    await this.registry.registerAgent(triageAgent, 100);

    // Register Emergency Agent (critical situations)
    const emergencyAgent = new EmergencyAgent(this.memory);
    await this.registry.registerAgent(emergencyAgent, 95);

    // Register General Practitioner Agent (primary care)
    const gpAgent = new GeneralPractitionerAgent(this.memory);
    await this.registry.registerAgent(gpAgent, 80);

    // Register Specialist Agents
    const cardiologistAgent = new CardiologistAgent(this.memory);
    await this.registry.registerAgent(cardiologistAgent, 85);

    const nutritionistAgent = new NutritionistAgent(this.memory);
    await this.registry.registerAgent(nutritionistAgent, 75);

    const mentalHealthAgent = new MentalHealthAgent(this.memory);
    await this.registry.registerAgent(mentalHealthAgent, 85);

    console.log('✅ All agents registered successfully (6 total agents)');
  }

  /**
   * Select the best agent for a request
   */
  private async selectAgent(request: OrchestrationRequest, conversationHistory: any[]): Promise<IAgent | null> {
    // For new conversations, start with triage agent
    if (conversationHistory.length === 0) {
      const triageAgents = this.registry.getAgentsByRole('triage');
      if (triageAgents.length > 0) {
        console.log('🏥 New conversation - routing to Triage Agent');
        return triageAgents[0];
      }
    }

    // Check if current agent can continue handling the request
    const currentAgentId = this.activeAgents.get(request.sessionId);
    if (currentAgentId) {
      const currentAgent = this.registry.getAgent(currentAgentId);
      if (currentAgent && currentAgent.canHandle({
        sessionId: request.sessionId,
        userMessage: request.userMessage,
        conversationHistory,
        urgencyLevel: request.urgencyLevel
      })) {
        console.log(`🔄 Continuing with current agent: ${currentAgent.name}`);
        return currentAgent;
      }
    }

    // Use registry to select best agent
    const agentRequest: AgentRequest = {
      sessionId: request.sessionId,
      userMessage: request.userMessage,
      conversationHistory,
      urgencyLevel: request.urgencyLevel,
      requestedCapabilities: request.requestedCapabilities
    };

    const selection = this.registry.selectAgent(agentRequest, {
      urgencyLevel: request.urgencyLevel,
      requireHealthy: true
    });

    if (selection) {
      console.log(`🎯 Selected agent: ${selection.agent.name} (confidence: ${selection.confidence.toFixed(2)})`);
      return selection.agent;
    }

    console.warn('⚠️ No suitable agent found');
    return null;
  }

  /**
   * Handle agent handoff process
   */
  private async handleAgentHandoff(
    sessionId: string,
    fromAgentId: string,
    toAgentId: string,
    reason: string,
    patientConsent: boolean = true
  ): Promise<void> {
    const fromAgent = this.registry.getAgent(fromAgentId);
    const toAgent = this.registry.getAgent(toAgentId);

    if (!fromAgent || !toAgent) {
      throw new Error('Invalid agent IDs for handoff');
    }

    // Create handoff context
    const handoffContext: AgentHandoffContext = {
      fromAgent,
      toAgent,
      reason,
      contextSummary: `Handoff from ${fromAgent.name} to ${toAgent.name}: ${reason}`,
      patientConsent,
      handoffTime: new Date().toISOString()
    };

    // Save handoff to history
    if (!this.handoffHistory.has(sessionId)) {
      this.handoffHistory.set(sessionId, []);
    }
    this.handoffHistory.get(sessionId)!.push(handoffContext);

    // Log handoff to memory for audit trail
    await this.memory.saveMessage(
      sessionId,
      'system',
      'orchestrator',
      'Agent Orchestrator',
      `AGENT HANDOFF: ${fromAgent.name} → ${toAgent.name}. Reason: ${reason}`,
      0, // System messages get priority sequence
      {
        handoffType: 'agent_transfer',
        fromAgent: fromAgent.id,
        toAgent: toAgent.id,
        reason,
        patientConsent,
        timestamp: handoffContext.handoffTime
      }
    );

    console.log(`🔄 Agent handoff logged: ${fromAgent.name} → ${toAgent.name}`);
  }

  /**
   * Save agent response to memory
   */
  private async saveAgentResponse(sessionId: string, response: AgentResponse): Promise<void> {
    const conversationHistory = await this.memory.getConversationHistory(sessionId);
    const sequenceNumber = conversationHistory.length + 1;

    await this.memory.saveMessage(
      sessionId,
      'agent',
      response.agentId,
      response.agentName,
      response.content,
      sequenceNumber,
      {
        confidence: response.confidence,
        reasoning: response.reasoning,
        emergencyFlags: response.emergencyFlags,
        suggestedHandoffs: response.suggestedHandoffs,
        followUpActions: response.followUpActions,
        ...response.metadata
      }
    );
  }

  /**
   * Shutdown the orchestrator
   */
  async shutdown(): Promise<void> {
    console.log('🛑 Shutting down Agent Orchestrator...');
    
    // Clear active sessions
    this.activeAgents.clear();
    this.handoffHistory.clear();

    // Registry will handle agent shutdown
    console.log('✅ Agent Orchestrator shutdown complete');
  }
}

// Export singleton instance
export const agentOrchestrator = new AgentOrchestrator();
export default agentOrchestrator;
