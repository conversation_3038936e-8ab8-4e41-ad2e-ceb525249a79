/**
 * REAL-TIME AGENT COMMUNICATION SERVICE
 * 
 * Provides WebSocket-based real-time communication for agent collaboration.
 * Enables live agent-to-agent messaging, collaboration updates, and
 * real-time consultation coordination.
 * 
 * FEATURES:
 * - Real-time agent messaging and collaboration
 * - Live consultation status updates
 * - Agent availability and presence tracking
 * - Emergency alert broadcasting
 * - Collaboration session management
 * - HIPAA-compliant real-time logging
 */

import { EventEmitter } from 'events';
import { agentCommunicationProtocol, type AgentMessage, type CollaborationSession } from './AgentCommunicationProtocol';
import { multiAgentCollaborationEngine, type MultiAgentCase } from './MultiAgentCollaborationEngine';
import { agentRegistry } from './AgentRegistry';

export interface RealTimeMessage {
  id: string;
  type: RealTimeMessageType;
  sessionId: string;
  fromAgentId?: string;
  toAgentId?: string;
  content: any;
  timestamp: string;
  priority: 'low' | 'medium' | 'high' | 'critical';
}

export type RealTimeMessageType = 
  | 'agent_message'
  | 'collaboration_update'
  | 'agent_status_change'
  | 'emergency_alert'
  | 'consultation_request'
  | 'consultation_response'
  | 'case_update'
  | 'system_notification';

export interface AgentPresence {
  agentId: string;
  agentName: string;
  status: 'online' | 'busy' | 'away' | 'offline';
  lastSeen: string;
  currentSessions: string[];
  availableForConsultation: boolean;
}

export interface CollaborationUpdate {
  collaborationId: string;
  sessionId: string;
  updateType: 'started' | 'agent_joined' | 'agent_left' | 'message_sent' | 'completed' | 'escalated';
  details: any;
  timestamp: string;
}

export class RealTimeAgentCommunication extends EventEmitter {
  private connections: Map<string, WebSocketConnection> = new Map(); // agentId -> connection
  private agentPresence: Map<string, AgentPresence> = new Map(); // agentId -> presence
  private activeCollaborations: Map<string, string[]> = new Map(); // collaborationId -> agentIds
  private messageQueue: Map<string, RealTimeMessage[]> = new Map(); // agentId -> queued messages

  constructor() {
    super();
    console.log('🔗 Initializing Real-Time Agent Communication...');
    this.setupEventHandlers();
    this.startPresenceMonitoring();
  }

  /**
   * Register an agent for real-time communication
   */
  registerAgent(agentId: string, agentName: string): void {
    console.log(`📡 Registering agent for real-time communication: ${agentName}`);

    const presence: AgentPresence = {
      agentId,
      agentName,
      status: 'online',
      lastSeen: new Date().toISOString(),
      currentSessions: [],
      availableForConsultation: true
    };

    this.agentPresence.set(agentId, presence);
    this.messageQueue.set(agentId, []);

    // Broadcast agent online status
    this.broadcastAgentStatusChange(agentId, 'online');
  }

  /**
   * Unregister an agent from real-time communication
   */
  unregisterAgent(agentId: string): void {
    const presence = this.agentPresence.get(agentId);
    if (presence) {
      console.log(`📡 Unregistering agent: ${presence.agentName}`);
      
      // Update status to offline
      presence.status = 'offline';
      presence.lastSeen = new Date().toISOString();
      
      // Broadcast offline status
      this.broadcastAgentStatusChange(agentId, 'offline');
      
      // Clean up
      this.connections.delete(agentId);
      this.messageQueue.delete(agentId);
    }
  }

  /**
   * Send real-time message to specific agent
   */
  async sendRealTimeMessage(message: Omit<RealTimeMessage, 'id' | 'timestamp'>): Promise<boolean> {
    try {
      const fullMessage: RealTimeMessage = {
        ...message,
        id: this.generateMessageId(),
        timestamp: new Date().toISOString()
      };

      console.log(`📨 Real-time message: ${message.type} to ${message.toAgentId || 'broadcast'}`);

      if (message.toAgentId) {
        // Send to specific agent
        return await this.deliverMessageToAgent(message.toAgentId, fullMessage);
      } else {
        // Broadcast to all agents in session
        return await this.broadcastToSession(message.sessionId, fullMessage);
      }

    } catch (error) {
      console.error('❌ Failed to send real-time message:', error);
      return false;
    }
  }

  /**
   * Broadcast collaboration update to all participating agents
   */
  async broadcastCollaborationUpdate(update: CollaborationUpdate): Promise<void> {
    try {
      const collaboratingAgents = this.activeCollaborations.get(update.collaborationId) || [];
      
      const message: RealTimeMessage = {
        id: this.generateMessageId(),
        type: 'collaboration_update',
        sessionId: update.sessionId,
        content: update,
        timestamp: new Date().toISOString(),
        priority: 'medium'
      };

      // Send to all collaborating agents
      for (const agentId of collaboratingAgents) {
        await this.deliverMessageToAgent(agentId, message);
      }

      console.log(`📢 Collaboration update broadcast to ${collaboratingAgents.length} agents`);

    } catch (error) {
      console.error('❌ Failed to broadcast collaboration update:', error);
    }
  }

  /**
   * Broadcast emergency alert to all available agents
   */
  async broadcastEmergencyAlert(sessionId: string, alertContent: any): Promise<void> {
    try {
      const emergencyMessage: RealTimeMessage = {
        id: this.generateMessageId(),
        type: 'emergency_alert',
        sessionId,
        content: alertContent,
        timestamp: new Date().toISOString(),
        priority: 'critical'
      };

      // Send to all online agents
      const onlineAgents = Array.from(this.agentPresence.values())
        .filter(presence => presence.status === 'online')
        .map(presence => presence.agentId);

      for (const agentId of onlineAgents) {
        await this.deliverMessageToAgent(agentId, emergencyMessage);
      }

      console.log(`🚨 Emergency alert broadcast to ${onlineAgents.length} agents`);

    } catch (error) {
      console.error('❌ Failed to broadcast emergency alert:', error);
    }
  }

  /**
   * Update agent status (online, busy, away, offline)
   */
  updateAgentStatus(agentId: string, status: AgentPresence['status']): void {
    const presence = this.agentPresence.get(agentId);
    if (presence) {
      presence.status = status;
      presence.lastSeen = new Date().toISOString();
      
      this.broadcastAgentStatusChange(agentId, status);
      console.log(`📊 Agent ${presence.agentName} status updated to: ${status}`);
    }
  }

  /**
   * Get current agent presence information
   */
  getAgentPresence(agentId: string): AgentPresence | null {
    return this.agentPresence.get(agentId) || null;
  }

  /**
   * Get all online agents
   */
  getOnlineAgents(): AgentPresence[] {
    return Array.from(this.agentPresence.values())
      .filter(presence => presence.status === 'online');
  }

  /**
   * Get available agents for consultation
   */
  getAvailableAgents(): AgentPresence[] {
    return Array.from(this.agentPresence.values())
      .filter(presence => 
        presence.status === 'online' && 
        presence.availableForConsultation
      );
  }

  /**
   * Start collaboration session tracking
   */
  startCollaborationTracking(collaborationId: string, participatingAgents: string[]): void {
    this.activeCollaborations.set(collaborationId, participatingAgents);
    
    // Update agent presence to show they're in collaboration
    participatingAgents.forEach(agentId => {
      const presence = this.agentPresence.get(agentId);
      if (presence) {
        presence.currentSessions.push(collaborationId);
        presence.availableForConsultation = false;
      }
    });

    console.log(`🤝 Started tracking collaboration: ${collaborationId} with ${participatingAgents.length} agents`);
  }

  /**
   * End collaboration session tracking
   */
  endCollaborationTracking(collaborationId: string): void {
    const participatingAgents = this.activeCollaborations.get(collaborationId) || [];
    
    // Update agent presence
    participatingAgents.forEach(agentId => {
      const presence = this.agentPresence.get(agentId);
      if (presence) {
        presence.currentSessions = presence.currentSessions.filter(id => id !== collaborationId);
        presence.availableForConsultation = presence.currentSessions.length === 0;
      }
    });

    this.activeCollaborations.delete(collaborationId);
    console.log(`🏁 Ended collaboration tracking: ${collaborationId}`);
  }

  /**
   * Deliver message to specific agent
   */
  private async deliverMessageToAgent(agentId: string, message: RealTimeMessage): Promise<boolean> {
    try {
      const connection = this.connections.get(agentId);
      
      if (connection && connection.isConnected) {
        // Send immediately via WebSocket
        connection.send(JSON.stringify(message));
        return true;
      } else {
        // Queue message for later delivery
        const queue = this.messageQueue.get(agentId) || [];
        queue.push(message);
        this.messageQueue.set(agentId, queue);
        
        console.log(`📬 Message queued for agent: ${agentId}`);
        return true;
      }

    } catch (error) {
      console.error(`❌ Failed to deliver message to agent ${agentId}:`, error);
      return false;
    }
  }

  /**
   * Broadcast message to all agents in a session
   */
  private async broadcastToSession(sessionId: string, message: RealTimeMessage): Promise<boolean> {
    try {
      // Find all agents currently involved in the session
      const sessionAgents = Array.from(this.agentPresence.values())
        .filter(presence => presence.currentSessions.includes(sessionId))
        .map(presence => presence.agentId);

      let successCount = 0;
      for (const agentId of sessionAgents) {
        const success = await this.deliverMessageToAgent(agentId, message);
        if (success) successCount++;
      }

      console.log(`📢 Broadcast to session ${sessionId}: ${successCount}/${sessionAgents.length} agents reached`);
      return successCount > 0;

    } catch (error) {
      console.error('❌ Failed to broadcast to session:', error);
      return false;
    }
  }

  /**
   * Broadcast agent status change
   */
  private broadcastAgentStatusChange(agentId: string, status: string): void {
    const presence = this.agentPresence.get(agentId);
    if (!presence) return;

    const statusMessage: RealTimeMessage = {
      id: this.generateMessageId(),
      type: 'agent_status_change',
      sessionId: 'system',
      content: {
        agentId,
        agentName: presence.agentName,
        status,
        timestamp: new Date().toISOString()
      },
      timestamp: new Date().toISOString(),
      priority: 'low'
    };

    // Broadcast to all other online agents
    const otherAgents = Array.from(this.agentPresence.keys())
      .filter(id => id !== agentId);

    otherAgents.forEach(async (otherAgentId) => {
      await this.deliverMessageToAgent(otherAgentId, statusMessage);
    });
  }

  /**
   * Setup event handlers for integration with other services
   */
  private setupEventHandlers(): void {
    // Listen for agent communication protocol events
    agentCommunicationProtocol.on('message', (agentMessage: AgentMessage) => {
      this.sendRealTimeMessage({
        type: 'agent_message',
        sessionId: agentMessage.sessionId,
        fromAgentId: agentMessage.fromAgentId,
        toAgentId: agentMessage.toAgentId,
        content: agentMessage,
        priority: agentMessage.priority
      });
    });

    agentCommunicationProtocol.on('collaboration_update', (collaboration: CollaborationSession) => {
      this.broadcastCollaborationUpdate({
        collaborationId: collaboration.id,
        sessionId: collaboration.sessionId,
        updateType: 'message_sent',
        details: collaboration,
        timestamp: new Date().toISOString()
      });
    });

    // Listen for multi-agent collaboration events
    multiAgentCollaborationEngine.on('collaboration_initiated', (case_: MultiAgentCase) => {
      this.startCollaborationTracking(case_.id, case_.assignedAgents);
    });

    multiAgentCollaborationEngine.on('consensus_reached', (case_: MultiAgentCase) => {
      this.endCollaborationTracking(case_.id);
    });
  }

  /**
   * Start presence monitoring (check agent availability)
   */
  private startPresenceMonitoring(): void {
    // Check agent presence every 30 seconds
    setInterval(() => {
      const now = new Date();
      const fiveMinutesAgo = new Date(now.getTime() - 5 * 60 * 1000);

      for (const [agentId, presence] of this.agentPresence.entries()) {
        const lastSeen = new Date(presence.lastSeen);
        
        // Mark agents as away if not seen for 5 minutes
        if (lastSeen < fiveMinutesAgo && presence.status === 'online') {
          this.updateAgentStatus(agentId, 'away');
        }
      }
    }, 30000); // 30 seconds
  }

  /**
   * Generate unique message ID
   */
  private generateMessageId(): string {
    return `rt_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Get real-time communication statistics
   */
  getStats(): {
    totalAgents: number;
    onlineAgents: number;
    activeCollaborations: number;
    queuedMessages: number;
  } {
    const queuedMessages = Array.from(this.messageQueue.values())
      .reduce((sum, queue) => sum + queue.length, 0);

    return {
      totalAgents: this.agentPresence.size,
      onlineAgents: this.getOnlineAgents().length,
      activeCollaborations: this.activeCollaborations.size,
      queuedMessages
    };
  }
}

// Simple WebSocket connection interface
interface WebSocketConnection {
  isConnected: boolean;
  send(data: string): void;
}

// Export singleton instance
export const realTimeAgentCommunication = new RealTimeAgentCommunication();
export default realTimeAgentCommunication;
